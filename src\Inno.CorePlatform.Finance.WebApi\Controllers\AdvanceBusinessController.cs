﻿using Amazon.Runtime;
using Dapr;
using Dapr.Client;
using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.Extensions;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 寄售垫资
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class AdvanceBusinessController : BaseController
    {
        private IAdvanceBusinessService _advanceBusinessService;
        private DaprClient _daprClient;
        private IServiceProvider _serviceProvider;
        private IEasyCachingProvider _easyCaching;
        private readonly IBaseAppService _baseAppService;
        public override bool EnableParameterLogging { get; set; } = true;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="advanceBusinessService"></param>
        /// <param name="daprClient"></param>
        public AdvanceBusinessController(IAdvanceBusinessService advanceBusinessService, DaprClient daprClient, IEasyCachingProvider easyCaching, IBaseAppService baseAppService, ISubLogService subLog) : base(subLog)
        {
            _advanceBusinessService = advanceBusinessService;
            _daprClient = daprClient;
            _easyCaching = easyCaching;
            _baseAppService = baseAppService;
        }


        [HttpPost("CreateAdvanceDetail")]
        [Topic("pubsub-default", "finance-finance-advance")]
        public async Task<IActionResult> CreateAdvanceDetail(List<Guid> creditIds)
        {
            try
            {
                await _baseAppService.CreateSubLog(SubLogSourceEnum.AdvanceCredit, JsonConvert.SerializeObject(creditIds), "admin", "垫资应收单订阅");
                await _advanceBusinessService.CreateAdvanceAsync(creditIds);
                return Ok("处理成功");
            }
            catch (Exception ex)
            {
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = Newtonsoft.Json.JsonConvert.SerializeObject(creditIds),
                    Topic = "finance-finance-advance",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/AdvanceBusiness/CreateAdvanceDetail"  //重试的回调方法路由 
                });
                return Ok("处理失败");
            }
        }
        /// <summary>
        /// 垫资单事件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("advanceSubAsync")]
        [Topic("pubsub-default", "pm-finance-advances")]
        public async Task<ActionResult> AdvanceSubAsync(List<AdvanceBusinessApply> input)
        {
            var jsonStr = JsonConvert.SerializeObject(input);
            var ret = BaseResponseData<int>.Failed(500, "操作失败");
            var cachekey = "pm-finance-advances" + input.FirstOrDefault()?.ProjectCode;
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    await _baseAppService.CreateSubLog(SubLogSourceEnum.Project, jsonStr, "admin", "项目-财务-垫资信息");
                    ret = await _advanceBusinessService.AdvanceSubAsync(input);
                    _easyCaching.Remove(cachekey);
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "pm-finance-advances",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/AdvanceBusiness/advanceSubAsync"  //重试的回调方法路由 
                });
                return Ok();
            }
        }

        [HttpPost("GetChinaMoney")]
        public async Task<BaseResponseData<string>> GetChinaMoney(decimal input)
        {
            return BaseResponseData<string>.Success(ChineseNumberConverter2.ConvertSum(input.ToString()));
        }
    }
}
