﻿using Org.BouncyCastle.Bcpg.Sig;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.Constant
{
    /// <summary>
    /// 能力中心
    /// </summary>
    public static class AppCenter
    {
        #region 基础数据
        /// <summary>
        /// 基础数据AppID
        /// </summary>
        public static string BDS_APPID => "bds-webapi";

        /// <summary>
        /// 用户所属公司
        /// </summary>
        public static string STAFF_COMPANIES => "/api/staffs/getStaffCompanies";
        /// <summary>
        /// 根据Type查找数据字典列表
        /// </summary>
        public static string GetDataDictionaryListByType => "/api/dataDictionarys/getList";
        /// <summary>
        /// 获取税收分类编码
        /// </summary>
        public static string getAllTaxClassCode => "api/products/getAllTaxClassCode";

        // <summary>
        /// 查询货号开票与税率数据信息
        /// </summary>
        public static string selectProductInvoices => "/api/productInvoicing/selectProductInvoices";
        /// <summary>
        /// 根据Type查找数据字典Meta
        /// </summary>
        public static string GetDataDictionaryMeta_Uri => "/api/dataDictionarys/meta";
        /// <summary>
        /// 用户所属事业部
        /// </summary>
        public static string STAFF_INFO => "/api/staffs/queryStaffInfo";
        /// <summary>
        /// 用户拥有的事业部权限
        /// </summary>
        public static string USER_DEPART_PERMISSION => "/api/departments/getBusiness";
        /// <summary>
        /// 获取核算部门列表
        /// </summary>
        public static string DEPART_LIST => "/api/departments/queryAll";
        /// <summary>
        /// 获取核算部门列表
        /// </summary>
        public static string DEPART_QUERY => "/api/departments/queryList";
        /// <summary>
        /// 非树形数据 获取核算部门，权限,业务权限过滤接口,
        /// </summary>
        public static string DEPART_GetFlatCheckedDepts => "/api/departments/getFlatCheckedDepts";
        /// <summary>
        /// 公司基本信息，用于给下拉框
        /// </summary>
        public static string COMPANY_META => "/api/companies/meta";
        /// <summary>
        /// 供应商信息
        /// </summary>
        public static string AGENT_META => "/api/agents/meta";

        /// <summary>
        /// 供应商信息
        /// </summary>
        public static string AGENT_QUERYITEM => "/api/agents/queryItem";
        /// <summary>
        /// 医院基本信息，用于给下拉框
        /// </summary>
        public static string HOSPITAL_META => "/api/customers/queryByIdsCustomerInfo";
        /// <summary>
        /// 厂家基本信息（带数据策略）
        /// </summary>
        public static string PRODUCER_META => "/api/producers/meta";

        /// <summary>
        /// 业务单元基本信息，用于给下拉框
        /// </summary>
        public static string SERVICE_META => "/api/businessUnits/meta";
        /// <summary>
        /// 查询业务单元详情
        /// </summary>
        public static string SERVICE_QUERY_ITEM = "/api/businessUnits/queryItem";
        /// <summary>
        /// 供应商经营范围
        /// </summary>
        public static string AGENT_INFO => "/api/agents/queryByIdsAgentInfo";
        /// <summary>
        /// 公司经营范围
        /// </summary>
        public static string COMPANY_INFO => "/api/companies/queryByIdsCompanyInfo";
        /// <summary>
        /// 查询公司信息
        /// </summary>
        public static string COMPANY_INFOMetaInfos => "/api/companies/metaInfos";

        /// <summary>
        /// 用户所属事业部
        /// </summary>
        public static string USER_STAFF_INFO => "/api/staffs/queryStaffInfo";
        /// <summary>
        /// 查询客户部门信息
        /// </summary>
        public static string CUSTOMER_DEPT_META => "/api/customerdepts/meta";
        /// <summary>
        /// 客户
        /// </summary>
        public static string CUSTOMER_META => "/api/customers/meta";
        /// <summary>
        /// 客户
        /// </summary>
        public static string getByCustomerId => "/api/customers/getByCustomerId/{0}";
        /// <summary>
        /// 客户收款信息查询
        /// </summary>
        public static string selectReceiptInfo => "/api/customers/selectReceiptInfo";
        /// <summary>
        /// 获取系统月度，单据日期
        /// </summary>
        public static string COMPANY_SYSTEM_MONTH => "/api/companies/getOrderDate";

        /// <summary>
        /// 查询所有公司
        /// </summary>
        public static string COMPANY_QueryItem => "/api/companies/queryItem";

        /// <summary>
        /// 员工基础信息元数据
        /// </summary>
        public static string STAFF_META => "/api/staffs/meta";

        /// <summary>
        /// 所有员工基础信息元数据
        /// </summary>
        public static string STAFF_ALL => "/api/staffs/all";

        /// <summary>
        /// 产品信息
        /// </summary>
        public static string PRODUCTNAMEMETA => "/api/productNames/queryDrugItem";

        /// <summary>
        /// 货号信息
        /// </summary>
        public static string PRODUCTINFO => "/api/products/queryByIdsProductInfoData";
        /// <summary>
        /// 货号信息
        /// </summary>
        public static string GetByNos => "/api/products/getProductTaxClassCode";

        /// <summary>
        /// Product分页查询
        /// </summary>
        public static string QueryProducts_Uri => "/api/products";

        /// <summary>
        /// 更新系统月度
        /// </summary>
        public static string UPDATESYSMONTH => "api/companies/changeSysMonth";

        #endregion

        /// <summary>
        /// 权限中心AppID
        /// </summary>
        public static string PERMISSION_APPID => "pc-webapi";
        /// <summary>
        /// 权限中心获取策略
        /// </summary>
        public static string PERMISSION_GET_STRATEGY => "/api/Strategy";
        public static string Business_QueryList => "/api/departments/queryList";

        #region 用户

        public static string UC_APPID => "uc-webapi";
        public static string UC_GETUSERBYIDS => "/api/users/getlistbyids";
        public static string UC_GETUSERBYNAMES => "/api/users/getlistbynames";
        public static string UC_GETSMALLUSERBYDISPLAYNAMES => "/api/users/GetSmallUsersByDisplayNames";
        #endregion

        #region 采购
        public static string Purchase_APPID => "purchase-webapi";
        public static string Purchase_GetById => "/api/PurchaseOrder/getById?Id={0}";
        public static string Purchase_GetSimpleByCode => "/api/PurchaseOrder/GetSimpleByCode/{0}";
        public static string Purchase_List => "/api/PurchaseOrder";
        public static string Purchase_GetConsignToPurchaseDetailGroup => "/api/PurchaseOrder/GetConsignToPurchaseDetailGroup";
        public static string Purchase_UpdateInvoiceQuantity => "/api/PurchaseOrder/UpdateInvoiceQuantity";

        public static string PurchaseReviseForInputBillQuery => "/api/PurchaseOrder/GetReviseOrderForInvoice";
        public static string GetRevisionPurchaseOrderList => "/api/PurchaseOrder/GetRevisionPurchaseOrderList";
        public static string Purchase_QueryDraftRebateSettlement => "/api/PurchaseOrder/QueryDraftRebateSettlement";
        public static string PurchaseReviseInputBillUpdate => "/api/PurchaseOrder/UpdateInvoiceAmount";
        public static string Purchase_QueryOrderDetailByPurchaseCodes => "/api/PurchaseOrder/Detail";

        /// <summary>
        /// 财务进项票多对多专用-获取寄售转购货明细
        /// </summary>
        public static string Purchase_GetConsignToPurchaseDetailGroupForInvoice => "/api/PurchaseFinance/GetConsignToPurchaseDetailGroupForInvoice";

        /// <summary>
        /// 财务进项票多对多专用-获取购货修订明细
        /// </summary>
        public static string Purchase_GetReviseOrderForFinanceInvoice => "/api/PurchaseFinance/GetReviseOrderForFinanceInvoice";

        /// <summary>
        /// 财务进项票多对多专用-更新寄售转购货
        /// </summary>
        public static string Purchase_UpdateConsignToPurchaseDetailGroupForInvoice => "/api/PurchaseFinance/UpdateConsignToPurchaseDetailGroupForInvoice";

        /// <summary>
        /// 财务进项票多对多专用-更新购货修订入票金额
        /// </summary>
        public static string Purchase_UpdateReviseOrderForFinanceInvoice => "/api/PurchaseFinance/UpdateReviseOrderForFinanceInvoice";

        public static string Purchase_ServiceGetById => "/api/ServicePurchase/GetById?Id={0}";

        /// <summary>
        /// 获取采购子系统配置接口路由
        /// </summary>
        public static string Purchase_SubSysRela => "/api/SubSysRela/query";

        /// <summary>
        /// 删除预付记录
        /// </summary>
        public static string Purchase_DeleteAdvancePayById => "/api/PurchaseOrder/deleteAdvancePayById?Id={0}";

        /// <summary>
        /// 生成购货修订采购订单
        /// </summary>
        public static string Purchase_GenerateAdvancePaymentRevise => "/api/PurchaseOrder/GenerateAdvancePaymentRevise";
        #endregion

        #region 入库申请
        public static string StoreInApply_APPID => "sia-webapi";
        public static string StoreInApply_GetById => "/api/PurchaseSubSystem/GetById?Id={0}";
        public static string StoreInApply_GetCostomsInfo => "/api/StoreInApply/getCustomsPaymentFinancePayInfoById?Id={0}";
        public static string StoreInApply_GetList => "/api/StoreInApply/GetList";
        #endregion

        #region 出库申请
        public static string StoreOutApply_APPID => "soa-webapi";
        public static string StoreOutApply_GetById => "/api/StoreOutApply/GetItem?Id={0}";
        public static string StoreOutApply_GetStoreOutApplyList => "/api/StoreOutApply/GetStoreOutApplyList";
        #endregion

        #region 库存
        public static string Inventory_APPID => "inventory-webapi";
        public static string Inventory_QueryStoreInByCode => "/api/storeIns/queryStoreInByCode?storeInCode={0}";
        public static string Inventory_QueryStoreInByCodes => "/api/storeIns/queryStoreInByCodes";
        public static string Inventory_QueryStoreOutByCode => "/api/storeOuts/queryByStoreOutCode?storeOutCode={0}";
        public static string Inventory_QueryStoreOutByCodes => "/api/storeOuts/queryByStoreOutCodes";
        /// <summary>
        /// 根据追溯码查询对应的入库信息
        /// </summary>
        public static string Inventory_QueryStoreInForFinance => "/api/storeIns/queryStoreInAndDetailForFinance";
        public static string Inventory_UpdateDetailsForFinance => "/api/storeIns/updateDetailsForFinance";

        /// <summary>
        /// 多对一勾稽查询入库单明细
        /// </summary>
        public static string Inventory_Many_QueryStoreInByCompany => "/api/storeIns/many/queryStoreInAndDetailForFinance";

        /// <summary>
        /// 多对一勾稽更新入库单明细
        /// </summary>
        public static string Inventory_Many_UpdateStoreInDetailsForFinance => "/api/storeIns/many/updateDetailsForFinance";

        /// <summary>
        /// 撤销入库单明细
        /// </summary>
        public static string Inventory_UpdateInvoiceInfoRevoke => "/api/storeIns/updateInvoiceInfoRevoke";

        /// <summary>
        /// 换货转退货明细
        /// </summary>
        public static string Inventory_QueryDetailForInputInvoice => "/api/storeExchangeBacks/queryDetailForInputInvoice";

        //经销调出-获取出库明细
        public static string Inventory_QueryStoreOutForFinance => "/api/storeOuts/queryStoreOutDetailForFinance";
        //经销调出-更新出库单明细
        public static string Inventory_UpdateStoreOutDetailsForFinance => "/api/storeOuts/updateDetailsForFinance";
        //经销调出-更新出库单明细（取消勾稽）
        public static string Inventory_UpdateInvoiceInfoRevokee => "/api/storeOuts/updateInvoiceInfoRevoke";
        /// <summary>
        /// 财务进项票多对多专用-获取出库明细
        /// </summary>
        public static string Inventory_Many_QueryStoreOutDetailForFinance => "/api/storeOuts/many/queryStoreOutDetailForFinance";

        /// <summary>
        /// 财务进项票多对多专用-更新出库单明细
        /// </summary>
        public static string Inventory_Many_UpdateStoreOutDetailsForFinance => "/api/storeOuts/many/updateDetailsForFinance";

        /// <summary>
        /// 财务进项票多对多专用-发票撤回,更新出库单明细发票信息
        /// </summary>
        public static string Inventory_Many_UpdateStoreOutInvoiceInfoRevoke => "/api/storeOuts/many/updateInvoiceInfoRevoke";

        /// <summary>
        /// 财务进项票多对多专用-获取换货转退货明细
        /// </summary>
        public static string Inventory_Many_QueryStoreExchangeBackDetailForFinance => "/api/storeExchangeBacks/many/queryDetailForInputInvoice";

        /// <summary>
        /// 财务进项票多对多专用-更新换货转退货单明细
        /// </summary>
        public static string Inventory_Many_UpdateStoreExchangeBackDetailsForFinance => "/api/storeExchangeBacks/many/updateDetailsInputInvoice";

        /// <summary>
        /// 财务进项票多对多专用-发票撤回,更新换货转退货明细发票信息
        /// </summary>
        public static string Inventory_Many_UpdateStoreExchangeBackInvoiceInfoRevoke => "/api/storeExchangeBacks/many/updateDetailsInputInvoiceRevoke";

        /// <summary>
        /// 财务进项票多对多专用-查询入库明细
        /// </summary>
        public static string Inventory_Many_QueryStoreInDetailForFinance => "/api/storeIns/many/queryStoreInDetailForFinance";

        /// <summary>
        /// 财务进项票多对多专用-更新入库明细
        /// </summary>
        public static string Inventory_Many_UpdateDetailsForFinance => "/api/storeIns/many/updateDetailsForFinance";


        public static string Invertory_QueryTraceInfo => "/api/storeTraces/queryTraceInfos";
        public static string Inventory_financeQueryStoreInByCode => "/api/finance/queryStoreInByCode";

        public static string ExchangeInventory => "/api/storeExchangeChecks/create";

        public static string SureIncomeInventory => "/api/storeOutChecks/waitFinancialInputCheck";

        public static string QueryReconForFm => "/api/store/queryReconForFm";
        public static string QueryIncomeCostRec => "/api/store/queryIncomeCostRec";

        public static string NoStoreRoomCompanys => "/api/storeChecks/filer/noStoreHouseCompany";

        public static string selectStoreOutByCompanyId => "/api/storeOuts/selectByCompanyId";

        public static string Inventory_QueryByCodeForFinance => "/api/storeExchangeBacks/queryByCodeForFinance?billCode={0}";

        public static string Inventory_QuerySaleRecallDetailRec => "/api/store/querySaleRecallDetailRec";
        public static string WaybillInfoValidForFinance => "/api/storeIns/waybillInfoValidForFinance";
        #endregion

        #region 销售
        public static string Sell_APPID => "sell-webapi";
        public static string Sell_TempSaleGetByCode => "/api/saleQuery/GetByCode?code={0}";
        public static string Sell_CreateSellRecognizeReceive => "/api/Receipt/Create";
        public static string Sell_PageQueryForFinances => "/api/saleQuery/pageQueryForFinances";
        public static string Sell_GetCustomerProductBilling => "/api/customerDiscount/get/billing";
        public static string Sell_CancelSellRecognizeReceive => "/api/Receipt/Cancel";
        public static string Sell_RecognizeFinished => "/api/Receipt/finished?recognizeCode={0}";
        public static string Sell_SalesDetailQuery => "/api/saleQuery/GetSaleDetails";
        public static string Sell_GetPreSaleListForProject => "/api/preSale/listForProject";
        public static string Sell_PageQueryForReconciliation => "/api/saleQuery/SaleForReconciliation";
        public static string Sell_GetSaleList => "/api/SaleOut/getSaleList";
        public static string Sell_GetSaleByCode => "/api/SaleQuery/getByCode";
        public static string Sell_GetRebateProvisionOfSale => "/api/PromotionalActivities/GetRebateProvisionOfSale";
        public static string Sell_GetTempDetailsBySaleDetailIds => "/api/saleQuery/GetTempDetailsBySaleDetailIds";
        public static string Sell_GetCompanyBySaleSystemId => "/api/saleSystem/{0}";
        public static string Sell_GetCompanyListBySaleSystemId => "api/saleSystem/page";
        public static string Sell_GetSaleAdvancePeriodInfo => "api/saleQuery/getSaleAdvancePeriodInfo";
        public static string Sell_GetSaleDetailsForFinance => "api/saleQuery/getSaleDetailsForFinance";
        #endregion

        #region 项目
        public static string PM_APPID => "pm-webapi";
        public static string PM_GetRefAccountAndDiscount => "/api/ProjectInfo/GetRefAccountAndDiscount";
        public static string PM_GetProjectInfo => "/api/ProjectInfo/GetProjectInfo";
        public static string PM_GetProductCostList => "/api/ProductCost/getlist";
        public static string PM_UpdatePaymentStatus => "/api/ProxyProject/UpdatePaymentStatus";
        public static string PM_GetRunProjectList => "/api/ProjectInfo/GetRunProjectList";
        public static string PM_GetContractDelayInfoList => "/api/ProjectInfo/GetContractDelayInfoList";
        public static string PM_GetProjectInfoMeta => "/api/ProjectInfo/meta";
        public static string PM_GetProjectListByBusinessDeptIds => "/api/ProjectInfo/GetProjectListByBusinessDeptIds";

        public static string PM_GetProjectList => "/api/ProjectInfo/GetProjectList";
        public static string PM_GetProjectInfoByIdOrName => "/api/ProjectInfo/GetProjectInfoByIdOrName";
        public static string PM_GetProjectLeader => "/api/ProjectInfo/GetLeadersByProjectIds";
        #endregion

        #region 跟台
        public static string Sginy_APPID => "sginy-webapi";
        public static string CreateSginyInventroy => "api/stageInventoryChecks/createInventory/";
        public static string FinanceSummary => "/api/stageInventoryChecks/financeSummary";
        public static string StageSurgery_SelectById => "/api/stageSurgery/selectById";

        public static string StageSurgeryToKingdee => "/api/stageSurgery/selectByCompanyId";
        #endregion

        #region 暂存
        public static string Tiny_APPID => "tiny-webapi";
        public static string CreateTinyInventroy => "api/stocktaking/form";
        public static string FinanceCostStatistics => "/api/tempinventory/financeCostStatistics";
        #endregion

        #region 管控
        public static string OM_APPID => "om-webapi";
        public static string OM_FinanceCheckPayQuota => "/api/purchasePay/financeCheckPayQuota";
        public static string OM_CheckCreditPayQuota => "/api/purchasePay/checkCreditPayQuota";
        #endregion

        #region 设备
        public static string Equip_APPID => "equip-webapi";

        public static string EquipIsAllFinishByPurchaseOrderCode = "/api/equipments/equipIsAllFinishByPurchaseOrderCode";

        /// <summary>
        /// 投放设备修订
        /// </summary>
        public static string UpdateEquipmentForPut = "/api/equipments/updateEquipmentForPut";
        #endregion

        #region 集成中心
        public static string IC_APPID => "ic-webapi";

        public static string ICSPDSupplierFinish = "/api/SpdInvoice/supplierFinish";

        public static string ICSPDWithInTemp = "/api/SpdInvoice/withInTemp";

        public static string ICGetSPDAmount = "/api/SpdApi/invoices";

        public static string GetAdvanceCheckDetai = "/api/BigDataReport/GetAdvanceCheckDetail";

        public static string IC_SubmitSunPurchaseInvoice = "/api/SunPurchaseInvoice/Push";

        public static string IC_SyncSunPurchaseInvoiceStatus = "/api/SunPurchaseInvoice/SearchInvoiceStatus";

        public static string PushRecognizeReceive = "/api/SpdInvoice/PushRecognizeReceive";

        public static string QueryApplyCodes = "/api/spdApi/queryApplyCodes";

        public static string ICSPDInvoiceApplyDetails = "/api/spdapi/invoiceApplyDetails";

        public static string AnthenUploadinvoice = "/api/SpdInvoiceAnthen/AnthenUploadinvoice";
        public static string VanxUploadinvoice = "/api/SpdInvoiceVanx/VanxUploadinvoice";
        public static string YiDaoUploadinvoice = "/api/SpdInvoiceYiDao/YiDaoUploadinvoice";
        /// <summary>
        /// 获取退回明细
        /// </summary>
        public static string GetRefundStoreInDetail = "/api/WangDianTong/getRefundStoreInDetail";
        #endregion

        #region 物流 - 阳采
        public static string Logistics_APPID => "logistics-webapi";

        public static string YCgetPsdByPurchaseCodeForPm = "/api/shycShipments/getPsdByPurchaseCodeForPm?purchaseCode={0}";

        public static string YCgetFullDetails = "/api/shycShipments/getFullDetails";

        public static string GetInvoiceShipmentExport = "/api/invoiceShipments/getInvoiceShipmentExport";

        public static string GetIsInvoiceDelivery = "/api/invoiceShipments/getIsInvoiceDelivery";

        public static string LOGISTICS_LETTERPDFDOWNLOAD => "/api/templates/getReportByConfig";
        #endregion

        #region 用户中心
        public static string USERCENTER_APPID => "uc-webapi";
        /// <summary>
        /// 根据EmployeeIds获取用户列表
        /// </summary>
        public static string UC_GET_USERLISTBYEMPLOYEEIDS => "/api/users/GetUserListByEmployeeIds";
        #endregion

        /// <summary>
        /// 发送消息提醒binding
        /// </summary>
        public static string BINDING_SENDNOTIFICATION => "binding-output-sendnotification";
    }
}
