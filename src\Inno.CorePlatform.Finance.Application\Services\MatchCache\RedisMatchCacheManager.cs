using EasyCaching.Core;
using Inno.CorePlatform.Finance.Application.DTOs.MergeInputBills;
using Inno.CorePlatform.Finance.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.Services.MatchCache
{
    /// <summary>
    /// Redis 匹配缓存管理器
    /// </summary>
    public class RedisMatchCacheManager : IMatchCacheManager
    {
        private readonly IEasyCachingProvider _cachingProvider;
        private const string SUBMIT_LOCK_PREFIX = "SubmitLock:";
        private const int SUBMIT_LOCK_EXPIRY_SECONDS = 10; // 锁的过期时间，10秒

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="cachingProviderFactory">缓存提供者工厂</param>
        public RedisMatchCacheManager(IEasyCachingProviderFactory cachingProviderFactory)
        {
            _cachingProvider = cachingProviderFactory.GetCachingProvider("default-redis");
        }

        /// <summary>
        /// 获取缓存键
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="businessType">业务类型</param>
        /// <param name="invoiceNumber">发票号（可选）</param>
        /// <returns>缓存键</returns>
        private string GetCacheKey(Guid mergeInputBillId, BusinessType businessType, string? invoiceNumber = null)
        {
            // 将GUID转换为大写
            string upperGuid = mergeInputBillId.ToString().ToUpper();

            // 如果提供了发票号，则将其添加到缓存键中
            if (!string.IsNullOrEmpty(invoiceNumber))
            {
                return $"MatchCache_{upperGuid}_{invoiceNumber}_{businessType}";
            }

            return $"MatchCache_{upperGuid}_{businessType}";
        }

        /// <summary>
        /// 获取匹配状态缓存键
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="invoiceNumber">发票号（可选）</param>
        /// <returns>匹配状态缓存键</returns>
        private string GetMatchStatusCacheKey(Guid mergeInputBillId, string? invoiceNumber = null)
        {
            // 将GUID转换为大写
            string upperGuid = mergeInputBillId.ToString().ToUpper();

            // 如果提供了发票号，则将其添加到缓存键中
            if (!string.IsNullOrEmpty(invoiceNumber))
            {
                return $"MatchStatus_{upperGuid}_{invoiceNumber}";
            }

            return $"MatchStatus_{upperGuid}";
        }

        /// <summary>
        /// 获取查询条件缓存键
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="invoiceNumber">发票号（可选）</param>
        /// <returns>查询条件缓存键</returns>
        private string GetQueryConditionCacheKey(Guid mergeInputBillId, string? invoiceNumber = null)
        {
            // 将GUID转换为大写
            string upperGuid = mergeInputBillId.ToString().ToUpper();

            // 如果提供了发票号，则将其添加到缓存键中
            if (!string.IsNullOrEmpty(invoiceNumber))
            {
                return $"QueryCondition_{upperGuid}_{invoiceNumber}";
            }

            return $"QueryCondition_{upperGuid}";
        }

        /// <summary>
        /// 获取业务类型缓存数据
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="businessType">业务类型</param>
        /// <returns>业务类型缓存数据</returns>
        public List<MatchableDocumentItem> GetBusinessTypeCache(Guid mergeInputBillId, BusinessType businessType)
        {
            try
            {
                var cacheKey = GetCacheKey(mergeInputBillId, businessType);
                var cacheValue = _cachingProvider.Get<List<MatchableDocumentItem>>(cacheKey);
                if (cacheValue.HasValue && cacheValue.Value != null)
                {
                    return cacheValue.Value;
                }
                return new List<MatchableDocumentItem>();
            }
            catch (Exception ex)
            {
                // 记录错误日志，但不抛出异常
                Console.WriteLine($"获取业务类型缓存数据失败: {ex.Message}");

                // 返回空列表
                return new List<MatchableDocumentItem>();
            }
        }

        /// <summary>
        /// 设置业务类型缓存数据
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="businessType">业务类型</param>
        /// <param name="data">缓存数据</param>
        public void SetBusinessTypeCache(Guid mergeInputBillId, BusinessType businessType, List<MatchableDocumentItem> data)
        {
            var cacheKey = GetCacheKey(mergeInputBillId, businessType);
            // 设置较长的过期时间，避免TimeSpan溢出问题
            // Redis最大支持49天的过期时间，这里设置为30天
            _cachingProvider.Set(cacheKey, data, TimeSpan.FromDays(49));

        }

        /// <summary>
        /// 获取所有缓存数据
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>所有缓存数据</returns>
        public Dictionary<BusinessType, List<MatchableDocumentItem>> GetAllCacheData(Guid mergeInputBillId)
        {
            var result = new Dictionary<BusinessType, List<MatchableDocumentItem>>();
            foreach (BusinessType businessType in Enum.GetValues(typeof(BusinessType)))
            {
                var cacheData = GetBusinessTypeCache(mergeInputBillId, businessType);
                // 无论是否有数据，都添加到结果中，确保所有业务类型都有对应的空数组
                result.Add(businessType, cacheData);
            }
            return result;
        }

        /// <summary>
        /// 清除所有缓存数据
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        public void ClearAllCache(Guid mergeInputBillId)
        {
            foreach (BusinessType businessType in Enum.GetValues(typeof(BusinessType)))
            {
                var cacheKey = GetCacheKey(mergeInputBillId, businessType);
                _cachingProvider.Remove(cacheKey);
            }

            var statusCacheKey = GetMatchStatusCacheKey(mergeInputBillId);
            _cachingProvider.Remove(statusCacheKey);

            // 清除查询条件缓存
            var queryConditionCacheKey = GetQueryConditionCacheKey(mergeInputBillId);
            _cachingProvider.Remove(queryConditionCacheKey);
        }


        /// <summary>
        /// 获取匹配状态缓存
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>匹配状态缓存</returns>
        public Dictionary<BusinessType, bool> GetMatchStatusCache(Guid mergeInputBillId)
        {
            var statusCacheKey = GetMatchStatusCacheKey(mergeInputBillId);
            var cacheValue = _cachingProvider.Get<Dictionary<BusinessType, bool>>(statusCacheKey);

            // 创建包含所有业务类型的字典
            var result = new Dictionary<BusinessType, bool>();
            foreach (BusinessType businessType in Enum.GetValues(typeof(BusinessType)))
            {
                result[businessType] = false; // 默认为false
            }

            // 如果缓存中有值，则更新对应的业务类型状态
            if (cacheValue.HasValue)
            {
                foreach (var item in cacheValue.Value)
                {
                    result[item.Key] = item.Value;
                }
            }

            return result;
        }

        /// <summary>
        /// 设置匹配状态缓存
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="businessType">业务类型</param>
        /// <param name="status">匹配状态</param>
        public void SetMatchStatusCache(Guid mergeInputBillId, BusinessType businessType, bool status)
        {
            try
            {
                var statusCacheKey = GetMatchStatusCacheKey(mergeInputBillId);

                // 获取当前缓存中的状态
                var currentStatus = GetMatchStatusCache(mergeInputBillId);

                // 更新指定业务类型的状态
                currentStatus[businessType] = status;

                // 保存回缓存
                _cachingProvider.Set(statusCacheKey, currentStatus, TimeSpan.FromDays(49));
            }
            catch (Exception ex)
            {
                // 记录错误日志，但不抛出异常
                Console.WriteLine($"设置匹配状态缓存失败: {ex.Message}");
            }
        }
        /// <summary>
        /// 获取已匹配的明细
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>已匹配的明细</returns>
        public List<MatchDetailItem> GetMatchedDetails(Guid mergeInputBillId)
        {
            var result = new List<MatchDetailItem>();
            var allCacheData = GetAllCacheData(mergeInputBillId);

            foreach (var kvp in allCacheData)
            {
                var businessType = kvp.Key;
                var cacheData = kvp.Value;

                foreach (var item in cacheData.Where(x => x.MatchQuantity > 0))
                {
                    result.Add(new MatchDetailItem
                    {
                        MergeInputBillDetailId = item.MergeInputBillDetailId, // 合并进项发票明细ID
                        ProductName = item.ProductName,
                        ProductNameId = item.ProductNameId,
                        BusinessType = businessType,
                        BusinessCode = item.BusinessCode,
                        BusinessDate = item.BusinessDate,
                        ProductNo = item.ProductNo ?? "",
                        ProductId = item.ProductId,
                        Specification = item.Specification ?? "",
                        Model = item.Model ?? "",
                        PurchaseOrderCode = item.PurchaseOrderCode,
                        ProducerOrderNo = item.ProducerOrderNo,
                        AvailableQuantity = item.AvailableQuantity,
                        TaxCost = item.TaxCost,
                        NoTaxCost = item.NoTaxCost,
                        TaxAmount = item.TaxAmount,
                        MatchQuantity = item.MatchQuantity,
                        MatchPrecision = item.MatchPrecision, // 使用缓存中的匹配精度
                        OperationType = 1, // 默认操作类型为1
                        TaxRate = item.TaxRate, // 使用缓存中的税率
                        InvoicedQuantity = item.InvoicedQuantity
                    });
                }
            }

            return result;
        }

        /// <summary>
        /// 将整个匹配结果存储到缓存中
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="businessType">业务类型</param>
        /// <param name="result">匹配结果</param>
        /// <param name="invoiceNumber">发票号（可选）</param>
        public void SaveMatchResultToCache(Guid mergeInputBillId, BusinessType businessType, Services.MatchLogic.MatchResult result, string? invoiceNumber = null)
        {
            // 将GUID转换为大写
            string upperGuid = mergeInputBillId.ToString().ToUpper();

            // 构建缓存键，如果提供了发票号，则将其添加到缓存键中
            string resultCacheKey;
            if (!string.IsNullOrEmpty(invoiceNumber))
            {
                resultCacheKey = $"MatchResult:{upperGuid}:{invoiceNumber}:{businessType}";
            }
            else
            {
                resultCacheKey = $"MatchResult:{upperGuid}:{businessType}";
            }

            // 直接将整个结果对象存储到缓存中，一次性覆盖
            _cachingProvider.Set(resultCacheKey, result, TimeSpan.FromHours(24));

            // 获取当前缓存中的数据
            var currentCacheItems = GetBusinessTypeCache(mergeInputBillId, businessType);

            // 只存储当前业务类型的匹配项
            var businessTypeItems = result.MatchedItems.Where(x => x.BusinessType == businessType).ToList();

            // 如果缓存中已有数据，需要合并而不是覆盖
            if (currentCacheItems != null && currentCacheItems.Count > 0)
            {
                // 创建一个字典，用于快速查找MatchKey对应的缓存项
                var cacheItemsByMatchKey = currentCacheItems
                    .Where(x => !string.IsNullOrEmpty(x.MatchKey))
                    .ToDictionary(x => x.MatchKey, x => x);

                // 更新或添加新的匹配项
                foreach (var item in businessTypeItems)
                {
                    if (!string.IsNullOrEmpty(item.MatchKey) && cacheItemsByMatchKey.TryGetValue(item.MatchKey, out var cacheItem))
                    {
                        // 更新现有项的MatchQuantity
                        cacheItem.MatchQuantity = item.MatchQuantity;
                    }
                    else
                    {
                        // 如果缓存中没有对应的项，则添加到缓存中
                        currentCacheItems.Add(item);
                    }
                }

                // 将合并后的数据存储到缓存中
                SetBusinessTypeCache(mergeInputBillId, businessType, currentCacheItems);
            }
            else
            {
                // 如果缓存中没有数据，直接使用当前的数据
                SetBusinessTypeCache(mergeInputBillId, businessType, businessTypeItems);
            }
        }

        /// <summary>
        /// 从缓存中获取匹配结果
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="businessType">业务类型</param>
        /// <param name="invoiceNumber">发票号（可选）</param>
        /// <returns>匹配结果，如果缓存中不存在则返回空的MatchResult对象</returns>
        public Services.MatchLogic.MatchResult? GetMatchResultFromCache(Guid mergeInputBillId, BusinessType businessType, string? invoiceNumber = null)
        {
            try
            {
                // 将GUID转换为大写
                string upperGuid = mergeInputBillId.ToString().ToUpper();

                // 构建缓存键，如果提供了发票号，则将其添加到缓存键中
                string resultCacheKey;
                if (!string.IsNullOrEmpty(invoiceNumber))
                {
                    resultCacheKey = $"MatchResult:{upperGuid}:{invoiceNumber}:{businessType}";
                }
                else
                {
                    resultCacheKey = $"MatchResult:{upperGuid}:{businessType}";
                }

                var cacheValue = _cachingProvider.Get<Services.MatchLogic.MatchResult>(resultCacheKey);

                if (cacheValue.HasValue)
                {
                    // 确保MatchedItems不为null
                    if (cacheValue.Value.MatchedItems == null)
                    {
                        cacheValue.Value.MatchedItems = new List<Application.DTOs.MergeInputBills.MatchableDocumentItem>();
                    }

                    // 确保UnmatchedDetails不为null
                    if (cacheValue.Value.UnmatchedDetails == null)
                    {
                        cacheValue.Value.UnmatchedDetails = new List<Application.DTOs.MergeInputBills.InvoiceDetailItem>();
                    }

                    return cacheValue.Value;
                }

                // 如果缓存中不存在，返回一个新的空MatchResult对象
                return new Services.MatchLogic.MatchResult
                {
                    MatchedItems = new List<Application.DTOs.MergeInputBills.MatchableDocumentItem>(),
                    UnmatchedDetails = new List<Application.DTOs.MergeInputBills.InvoiceDetailItem>(),
                    AllMatched = false
                };
            }
            catch (Exception ex)
            {
                // 记录错误日志，但不抛出异常
                Console.WriteLine($"从缓存中获取匹配结果失败: {ex.Message}");

                // 返回一个新的空MatchResult对象
                return new Services.MatchLogic.MatchResult
                {
                    MatchedItems = new List<Application.DTOs.MergeInputBills.MatchableDocumentItem>(),
                    UnmatchedDetails = new List<Application.DTOs.MergeInputBills.InvoiceDetailItem>(),
                    AllMatched = false
                };
            }
        }

        /// <summary>
        /// 根据条件查询缓存数据
        /// </summary>
        /// <param name="input">查询参数对象</param>
        /// <returns>符合条件的缓存数据</returns>
        public List<MatchableDocumentItem> QueryBusinessTypeCache(QueryCacheParams input)
        {
            // 获取所有缓存数据
            Dictionary<BusinessType, List<MatchableDocumentItem>> allCacheData;

            if (input.BusinessType.HasValue)
            {
                // 如果指定了业务类型，只获取该业务类型的缓存数据
                var cacheData = GetBusinessTypeCache(input.MergeInputBillId, input.BusinessType.Value);
                allCacheData = new Dictionary<BusinessType, List<MatchableDocumentItem>>
                {
                    { input.BusinessType.Value, cacheData }
                };
            }
            else
            {
                // 否则获取所有业务类型的缓存数据
                allCacheData = GetAllCacheData(input.MergeInputBillId);
            }

            // 合并所有业务类型的数据
            var result = new List<MatchableDocumentItem>();
            foreach (var data in allCacheData.Values)
            {
                result.AddRange(data);
            }

            // 应用筛选条件
            var query = result.AsQueryable();

            // 根据业务类型进行不同的过滤
            query = query.Where(x =>
                // 对于购货修订和服务费，过滤掉可用金额等于匹配金额的数据
                (x.BusinessType == BusinessType.PurchaseRevision || x.BusinessType == BusinessType.ServiceFeeProcurement||x.BusinessType==BusinessType.LossRecognition)
                    && x.TaxCost != 0
                // 对于其他业务类型，过滤掉 RemainingQuantity=0 的数据
                || (x.BusinessType != BusinessType.PurchaseRevision && x.BusinessType != BusinessType.ServiceFeeProcurement&&x.BusinessType!=BusinessType.LossRecognition)
                    && x.RemainingQuantity != 0
            );

            // 业务单号筛选，只使用业务明细单号
            if (!string.IsNullOrEmpty(input.BusinessItemCode))
            {
                query = query.Where(x => x.BusinessItemCode.Contains(input.BusinessItemCode));
            }
            // 兼容旧版本，如果BusinessItemCode为空但BusinessCode不为空，则使用BusinessCode
            else if (!string.IsNullOrEmpty(input.BusinessCode))
            {
                query = query.Where(x => x.BusinessItemCode.Contains(input.BusinessCode));
            }

            // 业务日期筛选
            if (input.BusinessDateStart.HasValue)
            {
                // 确保只比较日期部分，忽略时间部分
                var startDate = input.BusinessDateStart.Value.Date;
                query = query.Where(x => x.BusinessDate.Date >= startDate);
            }

            if (input.BusinessDateEnd.HasValue)
            {
                // 确保结束日期包含当天的所有时间（23:59:59）
                var endDate = input.BusinessDateEnd.Value.Date.AddDays(1).AddSeconds(-1);
                query = query.Where(x => x.BusinessDate.Date <= endDate.Date);
            }

            // 货号筛选
            if (!string.IsNullOrEmpty(input.ProductNo))
            {
                query = query.Where(x => !string.IsNullOrEmpty(x.ProductNo) && x.ProductNo.Contains(input.ProductNo));
            }

            // 产品名称筛选
            if (!string.IsNullOrEmpty(input.ProductName))
            {
                query = query.Where(x => !string.IsNullOrEmpty(x.ProductName) && x.ProductName.Contains(input.ProductName));
            }

            // 规格筛选
            if (!string.IsNullOrEmpty(input.Specification))
            {
                query = query.Where(x => !string.IsNullOrEmpty(x.Specification) && x.Specification.Contains(input.Specification));
            }

            // 型号筛选
            if (!string.IsNullOrEmpty(input.Model))
            {
                query = query.Where(x => !string.IsNullOrEmpty(x.Model) && x.Model.Contains(input.Model));
            }

            // 厂家名称筛选
            if (!string.IsNullOrEmpty(input.ProducerName))
            {
                query = query.Where(x => !string.IsNullOrEmpty(x.ProducerName) && x.ProducerName.Contains(input.ProducerName));
            }

            // 厂家订单号筛选
            if (!string.IsNullOrEmpty(input.ProducerOrderNo))
            {
                query = query.Where(x => !string.IsNullOrEmpty(x.ProducerOrderNo) && x.ProducerOrderNo.Contains(input.ProducerOrderNo));
            }

            // 采购订单号筛选
            if (!string.IsNullOrEmpty(input.PurchaseOrderCode))
            {
                query = query.Where(x => !string.IsNullOrEmpty(x.PurchaseOrderCode) && x.PurchaseOrderCode.Contains(input.PurchaseOrderCode));
            }

            // 税率筛选
            if (input.TaxRate.HasValue)
            {
                query = query.Where(x => x.TaxRate == input.TaxRate.Value);
            }

            // 按业务明细单号从小到大排序
            return query.OrderBy(x => x.BusinessItemCode).ToList();
        }

        /// <summary>
        /// 根据条件查询缓存数据（兼容旧版本）
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="businessType">业务类型</param>
        /// <param name="businessCode">业务单号</param>
        /// <param name="startDate">业务单据开始时间</param>
        /// <param name="endDate">业务单据结束时间</param>
        /// <param name="productNo">货号</param>
        /// <param name="productName">产品名称</param>
        /// <param name="specification">规格</param>
        /// <param name="model">型号</param>
        /// <param name="producerName">厂家名称</param>
        /// <param name="producerOrderNo">厂家订单号</param>
        /// <param name="purchaseOrderCode">采购订单号</param>
        /// <param name="taxRate">税率</param>
        /// <returns>符合条件的缓存数据</returns>
        public List<MatchableDocumentItem> QueryBusinessTypeCache(
            Guid mergeInputBillId,
            BusinessType? businessType = null,
            string? businessCode = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            string? productNo = null,
            string? productName = null,
            string? specification = null,
            string? model = null,
            string? producerName = null,
            string? producerOrderNo = null,
            string? purchaseOrderCode = null,
            decimal? taxRate = null)
        {
            // 创建查询参数对象
            var queryParams = new QueryCacheParams
            {
                MergeInputBillId = mergeInputBillId,
                BusinessType = businessType,
                BusinessItemCode = businessCode, // 使用businessCode作为BusinessItemCode
                BusinessDateStart = startDate,
                BusinessDateEnd = endDate,
                ProductNo = productNo,
                ProductName = productName,
                Specification = specification,
                Model = model,
                ProducerName = producerName,
                ProducerOrderNo = producerOrderNo,
                PurchaseOrderCode = purchaseOrderCode,
                TaxRate = taxRate
            };

            // 调用新版本的方法
            return QueryBusinessTypeCache(queryParams);
        }

        /// <summary>
        /// 保存查询条件到缓存
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="request">查询条件</param>
        public void SaveQueryConditionToCache(Guid mergeInputBillId, GetMatchableDocumentsRequest request)
        {
            try
            {
                var cacheKey = GetQueryConditionCacheKey(mergeInputBillId);
                // 设置较长的过期时间，与业务数据缓存保持一致
                _cachingProvider.Set(cacheKey, request, TimeSpan.FromDays(49));
            }
            catch (Exception ex)
            {
                // 记录错误日志，但不抛出异常
                Console.WriteLine($"保存查询条件到缓存失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从缓存中获取查询条件
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>查询条件，如果缓存中不存在则返回null</returns>
        public GetMatchableDocumentsRequest? GetQueryConditionFromCache(Guid mergeInputBillId)
        {
            try
            {
                var cacheKey = GetQueryConditionCacheKey(mergeInputBillId);
                var cacheValue = _cachingProvider.Get<GetMatchableDocumentsRequest>(cacheKey);

                if (cacheValue.HasValue)
                {
                    return cacheValue.Value;
                }

                return null;
            }
            catch (Exception ex)
            {
                // 记录错误日志，但不抛出异常
                Console.WriteLine($"从缓存中获取查询条件失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 检查是否有正在进行的提交操作
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>如果有正在进行的提交操作，返回发票号；否则返回null</returns>
        public string? GetLockedInvoiceNumber(Guid mergeInputBillId)
        {
            // 将GUID转换为大写
            string upperGuid = mergeInputBillId.ToString().ToUpper();
            var lockKey = $"{SUBMIT_LOCK_PREFIX}{upperGuid}";
            var cacheValue = _cachingProvider.Get<string>(lockKey);

            if (cacheValue.HasValue)
            {
                return cacheValue.Value;
            }

            return null;
        }

        /// <summary>
        /// 设置提交锁
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="invoiceNumber">发票号</param>
        public void SetSubmitLock(Guid mergeInputBillId, string invoiceNumber)
        {
            // 将GUID转换为大写
            string upperGuid = mergeInputBillId.ToString().ToUpper();
            var lockKey = $"{SUBMIT_LOCK_PREFIX}{upperGuid}";
            _cachingProvider.Set(lockKey, invoiceNumber, TimeSpan.FromSeconds(SUBMIT_LOCK_EXPIRY_SECONDS));
        }

        /// <summary>
        /// 释放提交锁
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        public void ReleaseSubmitLock(Guid mergeInputBillId)
        {
            // 将GUID转换为大写
            string upperGuid = mergeInputBillId.ToString().ToUpper();
            var lockKey = $"{SUBMIT_LOCK_PREFIX}{upperGuid}";
            _cachingProvider.Remove(lockKey);
        }

        // 进项票处理锁前缀
        private const string INPUT_BILL_PROCESSING_LOCK_PREFIX = "InputBillProcessingLock_";
        // 进项票处理锁过期时间（秒）
        private const int INPUT_BILL_PROCESSING_LOCK_EXPIRY_SECONDS = 300; // 5分钟

        /// <summary>
        /// 检查进项票是否正在处理中
        /// </summary>
        /// <param name="inputBillId">进项票ID</param>
        /// <returns>如果正在处理，返回发票号；否则返回null</returns>
        public string? GetInputBillProcessingLock(Guid inputBillId)
        {
            // 将GUID转换为大写
            string upperGuid = inputBillId.ToString().ToUpper();
            var lockKey = $"{INPUT_BILL_PROCESSING_LOCK_PREFIX}{upperGuid}";
            var cacheValue = _cachingProvider.Get<string>(lockKey);

            if (cacheValue.HasValue)
            {
                return cacheValue.Value;
            }

            return null;
        }

        /// <summary>
        /// 设置进项票处理锁
        /// </summary>
        /// <param name="inputBillId">进项票ID</param>
        /// <param name="invoiceNumber">发票号</param>
        public void SetInputBillProcessingLock(Guid inputBillId, string invoiceNumber)
        {
            // 将GUID转换为大写
            string upperGuid = inputBillId.ToString().ToUpper();
            var lockKey = $"{INPUT_BILL_PROCESSING_LOCK_PREFIX}{upperGuid}";
            _cachingProvider.Set(lockKey, invoiceNumber, TimeSpan.FromSeconds(INPUT_BILL_PROCESSING_LOCK_EXPIRY_SECONDS));
        }

        /// <summary>
        /// 释放进项票处理锁
        /// </summary>
        /// <param name="inputBillId">进项票ID</param>
        public void ReleaseInputBillProcessingLock(Guid inputBillId)
        {
            // 将GUID转换为大写
            string upperGuid = inputBillId.ToString().ToUpper();
            var lockKey = $"{INPUT_BILL_PROCESSING_LOCK_PREFIX}{upperGuid}";
            _cachingProvider.Remove(lockKey);
        }
    }
}
