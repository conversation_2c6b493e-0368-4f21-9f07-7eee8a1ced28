﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.Extensions
{
    public class ChineseNumberConverter
    {
        //public static string GetChinaMoney(decimal money)
        //{
        //    string[] strArray;
        //    string str = "";
        //    string str2 = "";
        //    string str3 = money.ToString("0.00");
        //    switch (str3.Trim().Length)
        //    {
        //        case 4:
        //            strArray = new string[] { str3[0].ToString(), "y", str3[2].ToString(), "j", str3[3].ToString(), "f" };
        //            str = string.Concat(strArray);
        //            break;

        //        case 5:
        //            strArray = new string[] { str3[0].ToString(), "s", str3[1].ToString(), "y", str3[3].ToString(), "j", str3[4].ToString(), "f" };
        //            str = string.Concat(strArray);
        //            break;

        //        case 6:
        //            strArray = new string[] { str3[0].ToString(), "b", str3[1].ToString(), "s", str3[2].ToString(), "y", str3[4].ToString(), "j", str3[5].ToString(), "f" };
        //            str = string.Concat(strArray);
        //            break;

        //        case 7:
        //            strArray = new string[] { str3[0].ToString(), "q", str3[1].ToString(), "b", str3[2].ToString(), "s", str3[3].ToString(), "y", str3[5].ToString(), "j", str3[6].ToString(), "f" };
        //            str = string.Concat(strArray);
        //            break;

        //        case 8:
        //            strArray = new string[] { str3[0].ToString(), "w", str3[1].ToString(), "q", str3[2].ToString(), "b", str3[3].ToString(), "s", str3[4].ToString(), "y", str3[6].ToString(), "j", str3[7].ToString(), "f" };
        //            str = string.Concat(strArray);
        //            break;

        //        case 9:
        //            strArray = new string[] { str3[0].ToString(), "s", str3[1].ToString(), "w", str3[2].ToString(), "q", str3[3].ToString(), "b", str3[4].ToString(), "s", str3[5].ToString(), "y", str3[7].ToString(), "j", str3[8].ToString(), "f" };
        //            str = string.Concat(strArray);
        //            break;

        //        case 10:
        //            strArray = new string[] {
        //                str3[0].ToString(), "b", str3[1].ToString(), "s", str3[2].ToString(), "w", str3[3].ToString(), "q", str3[4].ToString(), "b", str3[5].ToString(), "s", str3[6].ToString(), "y", str3[8].ToString(), "j",
        //                str3[9].ToString(), "f"
        //             };
        //            str = string.Concat(strArray);
        //            break;

        //        case 11:
        //            strArray = new string[] {
        //                str3[0].ToString(), "q", str3[1].ToString(), "b", str3[2].ToString(), "s", str3[3].ToString(), "w", str3[4].ToString(), "q", str3[5].ToString(), "b", str3[6].ToString(), "s", str3[7].ToString(), "y",
        //                str3[9].ToString(), "j", str3[10].ToString(), "f"
        //             };
        //            str = string.Concat(strArray);
        //            break;

        //        case 12:
        //            strArray = new string[] {
        //                str3[0].ToString(), "m", str3[1].ToString(), "q", str3[2].ToString(), "b", str3[3].ToString(), "s", str3[4].ToString(), "w", str3[5].ToString(), "q", str3[6].ToString(), "b", str3[7].ToString(), "s",
        //                str3[8].ToString(), "y", str3[10].ToString(), "j", str3[11].ToString(), "f"
        //             };
        //            str = string.Concat(strArray);
        //            break;
        //    }
        //    for (int i = 0; i < str.Trim().Length; i++)
        //    {
        //        switch (str[i])
        //        {
        //            case '0':
        //                str2 = str2 + "零";
        //                break;

        //            case '1':
        //                str2 = str2 + "壹";
        //                break;

        //            case '2':
        //                str2 = str2 + "贰";
        //                break;

        //            case '3':
        //                str2 = str2 + "叁";
        //                break;

        //            case '4':
        //                str2 = str2 + "肆";
        //                break;

        //            case '5':
        //                str2 = str2 + "伍";
        //                break;

        //            case '6':
        //                str2 = str2 + "陆";
        //                break;

        //            case '7':
        //                str2 = str2 + "柒";
        //                break;

        //            case '8':
        //                str2 = str2 + "捌";
        //                break;

        //            case '9':
        //                str2 = str2 + "玖";
        //                break;

        //            case 'b':
        //                str2 = str2 + "佰";
        //                break;

        //            case 'f':
        //                str2 = str2 + "分";
        //                break;

        //            case 'j':
        //                str2 = str2 + "角";
        //                break;

        //            case 'm':
        //                str2 = str2 + "亿";
        //                break;

        //            case 'q':
        //                str2 = str2 + "仟";
        //                break;

        //            case 's':
        //                str2 = str2 + "拾";
        //                break;

        //            case 'w':
        //                str2 = str2 + "万";
        //                break;

        //            case 'y':
        //                str2 = str2 + "元";
        //                break;
        //        }
        //    }
        //    return str2;
        //}
        private static readonly string[] NumMap = { "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖" };
        private static readonly string[] UnitMap = { "", "拾", "佰", "仟" };
        private static readonly string[] LevelMap = { "", "万", "亿", "万亿" };

        public static string GetChinaMoney(decimal number)
        {
            if (number == 0)
                return "零元整";

            bool isNegative = number < 0;
            number = Math.Abs(number);

            var integerPart = (long)Math.Truncate(number);
            var decimalPart = (int)Math.Round((number - integerPart) * 100);

            var integerStr = ConvertIntegerToChinese(integerPart);
            var result = new StringBuilder();

            if (isNegative)
                result.Append("负");

            result.Append(integerStr).Append("元");

            if (decimalPart > 0)
            {
                int jiao = decimalPart / 10;
                int fen = decimalPart % 10;

                if (jiao > 0)
                    result.Append(NumMap[jiao]).Append("角");
                if (fen > 0)
                    result.Append(NumMap[fen]).Append("分");
            }
            else
            {
                result.Append("整");
            }

            string final = result.ToString()
                .Replace("零角", "")
                .Replace("零售", "零")
                .Replace("角分", "分")
                .Replace("零分", "分")
                .Replace("零零", "零")
                .Trim();

            if (final.EndsWith("元"))
                final += "整";

            return final;
        }

        private static string ConvertIntegerToChinese(long number)
        {
            if (number == 0)
                return "零";

            var result = new StringBuilder();
            int levelIndex = 0;

            while (number > 0)
            {
                var thousandPart = number % 10000;
                number /= 10000;

                if (thousandPart > 0)
                {
                    string partStr = ConvertThousand(thousandPart);
                    string levelStr = LevelMap[levelIndex];

                    // ✅ 只有当前部分不是“零”时才插入“零”
                    if (levelIndex > 0 && result.Length > 0 && !result.ToString().EndsWith("万") && !result.ToString().EndsWith("亿") && !result.ToString().EndsWith("万亿"))
                    {
                        result.Insert(0, "零");
                    }

                    result.Insert(0, partStr + levelStr);
                }

                levelIndex++;
            }

            return result.ToString();
        }

        private static string ConvertThousand(long number)
        {
            if (number == 0)
                return "零";

            var result = new StringBuilder();
            bool[] digitExists = new bool[4]; // 记录每一位是否非零

            for (int pos = 3; pos >= 0; pos--)
            {
                long digit = (number / (long)Math.Pow(10, pos)) % 10;

                if (digit > 0)
                {
                    // 插入“零”仅当前面有非零位但当前位为零
                    int zeros = 0;
                    for (int i = 3; i > pos; i--)
                    {
                        if ((number / (long)Math.Pow(10, i)) % 10 == 0)
                            zeros++;
                    }

                    for (int i = 0; i < zeros; i++)
                    {
                        if (result.Length == 0 || result[^1] != '零')
                            result.Append("零");
                    }

                    result.Append(NumMap[digit]);
                    if (pos > 0)
                        result.Append(UnitMap[pos]);

                    digitExists[pos] = true;
                }
            }

            string temp = result.ToString().Replace("零零", "零").Trim('零');

            return temp.Length > 0 ? temp : "零";
        }
    }
    public class ChineseNumberConverter2
    { 

        /// 转换数字金额主函数（包括小数）
        /// 数字字符串
        /// 转换成中文大写后的字符串或者出错信息提示字符串
        public static string ConvertSum(string str)
        {
            if (!IsPositveDecimal(str))
            {
                return "输入的不是正数字！";
            }
            if (Double.Parse(str) > 999999999999.99)
            {
                return "数字太大，无法换算，请输入一万亿元以下的金额";
            }
            char[] ch = new char[1];
            ch[0] = '.'; //小数点    
            string[] splitstr = null; //定义按小数点分割后的字符串数组  
            splitstr = str.Split(ch[0]);//按小数点分割字符串 
            if (splitstr.Length == 1) //只有整数部分  
            {
                return ConvertData(str).Replace("零亿", "零亿").Replace("零万", "万零").Replace("零仟", "仟零") + "圆整";
            }
            else //有小数部分           
            {
                string rstr;
                rstr = ConvertData(splitstr[0]) + "圆";//转换整数部分 
                rstr += ConvertXiaoShu(splitstr[1]);//转换小数部分  
                return rstr.Replace("零亿", "零亿").Replace("零万", "万零").Replace("零仟", "仟零");
            }
        }

        /// 判断是否是正数字字符串  
        /// 判断字符串 
        /// 如果是数字，返回true，否则返回false
        public static bool IsPositveDecimal(string str)
        {
            Decimal d;
            try
            {
                d = Decimal.Parse(str);
            }
            catch (Exception)
            {
                return false;
            }
            if (d > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// 转换数字（整数）
        /// 需要转换的整数数字字符串   
        /// 转换成中文大写后的字符串    
        public static string ConvertData(string str)
        {
            string tmpstr = "";
            string rstr = "";
            int strlen = str.Length;
            if (strlen <= 4)//数字长度小于四位
            {
                rstr = ConvertDigit(str);
            }
            else
            {
                if (strlen <= 8)//数字长度大于四位，小于八位
                {
                    tmpstr = str.Substring(strlen - 4, 4);//先截取最后四位数字
                    rstr = ConvertDigit(tmpstr);//转换最后四位数字    
                    tmpstr = str.Substring(0, strlen - 4);//截取其余数字 
                                                          //将两次转换的数字加上万后相连接             
                    rstr = String.Concat(ConvertDigit(tmpstr) + "万", rstr);
                    rstr = rstr.Replace("零零", "零");
                }
                else if (strlen <= 12)//数字长度大于八位，小于十二位  
                {
                    tmpstr = str.Substring(strlen - 4, 4);//先截取最后四位数字
                    rstr = ConvertDigit(tmpstr);//转换最后四位数字        
                    tmpstr = str.Substring(strlen - 8, 4);//再截取四位数字  
                    rstr = String.Concat(ConvertDigit(tmpstr) + "万", rstr);
                    tmpstr = str.Substring(0, strlen - 8);
                    rstr = String.Concat(ConvertDigit(tmpstr) + "亿", rstr);
                    rstr = rstr.Replace("零亿", "亿");
                    rstr = rstr.Replace("零万", "零");
                    rstr = rstr.Replace("零零", "零");
                    rstr = rstr.Replace("零零", "零");
                }
            }
            strlen = rstr.Length;
            if (strlen >= 2)
            {
                switch (rstr.Substring(strlen - 2, 2))
                {
                    case "佰零":
                        rstr = rstr.Substring(0, strlen - 2) + "佰";
                        break;
                    case "仟零":
                        rstr = rstr.Substring(0, strlen - 2) + "仟";
                        break;
                    case "万零":
                        rstr = rstr.Substring(0, strlen - 2) + "万";
                        break;
                    case "亿零":
                        rstr = rstr.Substring(0, strlen - 2) + "亿";
                        break;
                }
            }
            return rstr;
        }

        /// 转换数字（小数部分）     
        /// 需要转换的小数部分数字字符串 
        /// 转换成中文大写后的字符串 
        public static string ConvertXiaoShu(string str)
        {
            int strlen = str.Length;
            string rstr;
            if (strlen == 1)
            {
                rstr = ConvertChinese(str) + "角";
                return rstr;
            }
            else
            {
                string tmtr = str.Substring(0, 1);
                rstr = ConvertChinese(tmtr) + "角";
                tmtr = str.Substring(1, 1);
                rstr += ConvertChinese(tmtr) + "分";
                rstr = rstr.Replace("零分", "");
                rstr = rstr.Replace("零角", "");
                return rstr;
            }
        }

        /// 转换数字   
        /// 转换的字符串（四位以内） 
        public static string ConvertDigit(string str)
        {
            int strlen = str.Length;
            string rstr = "";
            switch (strlen)
            {
                case 1:
                    rstr = ConvertChinese(str);
                    break;
                case 2:
                    rstr = Convert2Digit(str);
                    break;
                case 3:
                    rstr = Convert3Digit(str);
                    break;
                case 4:
                    rstr = Convert4Digit(str);
                    break;
            }
            rstr = rstr.Replace("拾零", "拾");
            strlen = rstr.Length;
            return rstr;
        }

        ///转换四位数字  
        public static string Convert4Digit(string str)
        {
            string str1 = str.Substring(0, 1);
            string str2 = str.Substring(1, 1);
            string str3 = str.Substring(2, 1);
            string str4 = str.Substring(3, 1);
            string rstring = "";
            rstring += ConvertChinese(str1) + "仟";
            rstring += ConvertChinese(str2) + "佰";
            rstring += ConvertChinese(str3) + "拾";
            rstring += ConvertChinese(str4);
            rstring = rstring.Replace("零仟", "零");
            rstring = rstring.Replace("零佰", "零");
            rstring = rstring.Replace("零拾", "零");
            rstring = rstring.Replace("零零", "零");
            rstring = rstring.Replace("零零", "零");
            rstring = rstring.Replace("零零", "零");
            return rstring;
        }

        /// 转换三位数字 
        public static string Convert3Digit(string str)
        {
            string str1 = str.Substring(0, 1);
            string str2 = str.Substring(1, 1);
            string str3 = str.Substring(2, 1);
            string rstring = "";
            rstring += ConvertChinese(str1) + "佰";
            rstring += ConvertChinese(str2) + "拾";
            rstring += ConvertChinese(str3);
            rstring = rstring.Replace("零佰", "零");
            rstring = rstring.Replace("零拾", "零");
            rstring = rstring.Replace("零零", "零");
            rstring = rstring.Replace("零零", "零");
            return rstring;
        }

        /// 转换二位数字  
        public static string Convert2Digit(string str)
        {
            string str1 = str.Substring(0, 1);
            string str2 = str.Substring(1, 1);
            string rstring = "";
            rstring += ConvertChinese(str1) + "拾";
            rstring += ConvertChinese(str2);
            rstring = rstring.Replace("零拾", "零");
            rstring = rstring.Replace("零零", "零");
            return rstring;
        }

        /// 将一位数字转换成中文大写数字  
        public static string ConvertChinese(string str)
        {
            //"零壹贰叁肆伍陆柒捌玖拾佰仟万亿圆整角分"      
            string cstr = "";
            switch (str)
            {
                case "0":
                    cstr = "零";
                    break;
                case "1":
                    cstr = "壹";
                    break;
                case "2":
                    cstr = "贰";
                    break;
                case "3":
                    cstr = "叁";
                    break;
                case "4":
                    cstr = "肆";
                    break;
                case "5":
                    cstr = "伍";
                    break;
                case "6":
                    cstr = "陆";
                    break;
                case "7":
                    cstr = "柒";
                    break;
                case "8":
                    cstr = "捌";
                    break;
                case "9":
                    cstr = "玖";
                    break;
            }
            return (cstr);
        }
    }
}
