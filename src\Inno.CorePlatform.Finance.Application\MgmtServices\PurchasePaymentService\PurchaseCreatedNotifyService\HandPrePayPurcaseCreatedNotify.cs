using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.MgmtServices.PurchasePaymentService.PurchaseCreatedNotifyService.PaymentOrderHandService;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NetTopologySuite.Index.HPRtree;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.PurchasePaymentService.PurchaseCreatedNotifyService
{
    /// <summary>
    /// 有预付申请的处理
    /// </summary>
    public class HandPrePayPurcaseCreatedNotify : HandPurchaseCreatedNotifyBase
    {
        protected override async Task CheckPaymentCodesIsHanding()
        {
            if (_easyCaching != null)
            {
                if (!string.IsNullOrWhiteSpace(CurrentContent.PaymentCodes))
                {
                    //随机最多等5s
                    await Task.Delay(new TimeSpan(0, 0, new Random().Next(5)));
                    PaymentCodes = CurrentContent.PaymentCodes.Split(",").ToList();
                    Dictionary<string,string> keyValuePairs = new Dictionary<string, string>();
                    foreach (var item in PaymentCodes)
                    {
                        keyValuePairs.Add(item, item);
                    }
                    var cacheValues = await _easyCaching.GetAllAsync<string>(PaymentCodes);
                    if (cacheValues != null && cacheValues.Any(r => !string.IsNullOrWhiteSpace(r.Value.Value))) 
                    {
                        var hadingKeys = cacheValues.Where(r=>!string.IsNullOrWhiteSpace(r.Value.Value)).Select(r=>r.Key).ToList();
                        throw new ApplicationException($"付款单号：{string.Join(",", hadingKeys)} 正在处理中，请稍后再试...");
                    }
                    else
                    {
                        await _easyCaching.SetAllAsync(keyValuePairs,new TimeSpan(12,0,0));
                    }
                }
            }
            else
            {
                throw new NullReferenceException("缓存服务[IEasyCachingProvider]不能为空。");
            }
        }
        protected override async Task<bool> CheckPrePayIsHanded()
        {
            if (CurrentContent.RelateId.HasValue)
            {
                IBaseAllQueryService<PaymentPo> _paymentQueryService = _serviceProvider.GetService<IBaseAllQueryService<PaymentPo>>();

                var paymentdb = await _paymentQueryService.FirstOrDefaultAsync(p => p.RelateId == CurrentContent.RelateId);
                if (paymentdb != null) return true;
                var debtPayment = await _dbContext.DebtPaymentUseDetails.FirstOrDefaultAsync(p => p.RelateId == CurrentContent.RelateId);
                if (debtPayment != null) return true;
            }
            return false;
        }
        protected override async Task PaymentOrderHand()
        {
            var advanceItem = CurrentPurchaseOrder.AdvancePays.Where(r => r.Id == CurrentContent.RelateId).FirstOrDefault();
            if (PaymentCodes!=null&&PaymentCodes.Count>0)
            {//预付且有选付款单
                List<PaymentOrderHandler> listHandlers = new List<PaymentOrderHandler>();
                var listPaymentCodes = advanceItem.PaymentCodes.OrderBy(r => r.Value).ToList();
                //付款单处理顺序规则
                var _paymentQueryService = _serviceProvider.GetService<IBaseAllQueryService<PaymentPo>>();
                foreach (var item in listPaymentCodes)
                {
                    if (item.TypeName == "游离付款单")
                    {//如果采购标识的是游离付款单，但是财务的游离付款单中没有这个单的数据，则将其标识为购货修订单处理（负数应付）
                        bool isExsit = await _dbContext.Payments.AnyAsync(r => r.Code == item.Code);
                        if (!isExsit)
                        {
                            item.TypeName = "购货修订单";
                        }
                    }
                }
                Dictionary<string, int> sortOrder = new Dictionary<string, int>
                {
                    { "游离付款单", 0 },
                    { "返利单", 1 },
                    { "购货修订单", 2 },
                    { "同企业应收", 3 }
                };
                listPaymentCodes = listPaymentCodes
                    .OrderBy(r => sortOrder[r.TypeName])
                    .ToList();
                decimal advanceActualPayAmount = decimal.Parse(advanceItem.ActualPayAmount.ToString("F2"));
                foreach (var paymentCodeItem in listPaymentCodes)
                {
                    switch (paymentCodeItem.TypeName)
                    {
                        case "游离付款单":
                            listHandlers.Add(new DissociatedPaymentOrderHandler(advanceActualPayAmount, paymentCodeItem.Code,_serviceProvider,CurrentPurchaseOrder, advanceItem));
                            break;
                        case "返利单":
                            listHandlers.Add(new RebatePaymentOrderHandler(advanceActualPayAmount, paymentCodeItem.Code,_serviceProvider,CurrentPurchaseOrder, advanceItem));
                            break;
                        case "同企业应收":
                            listHandlers.Add(new SameEnterpriseReceiptOrderHandler(advanceActualPayAmount, paymentCodeItem.Code,_serviceProvider,CurrentPurchaseOrder, advanceItem));
                            break;
                        case "购货修订单":
                            listHandlers.Add(new PurchaseRevisePaymentOrderHandler(advanceActualPayAmount, paymentCodeItem.Code,_serviceProvider,CurrentPurchaseOrder, advanceItem));
                            break;
                        default:
                            break;
                    }
                }
                //配置责任链
                for (int i = 0; i < listHandlers.Count; i++)
                {
                    if (i + 1 < listHandlers.Count)
                    {
                        listHandlers[i].NextHandler = listHandlers[i + 1];
                    }
                }
                ///最后剩余金额
                var remainingAmount = await listHandlers[0].HandPaymentOrder();
                if (remainingAmount>0)
                {
                    //推送金蝶，生成一张新的付款单
                    _logger.LogInformation($"付款金额不足，新增金蝶付款单：{CurrentContent.BusinessCode}**{CurrentContent.RelateId}");
                    var payment = await AddPayment(advanceItem, CurrentPurchaseOrder, decimal.Parse(remainingAmount.ToString("F2")), CurrentPurchaseOrder.CreatedBy, false, DateTime.MinValue);
                    if (payment.Value > 0)
                    {
                        //采购付款计划推送金蝶
                        var ret = await PushPurchasePayPlans(payment, advanceItem);
                        if (ret.Code == CodeStatusEnum.Failed)
                        {
                            throw new Exception(ret.Message);
                        }
                    }
                }
                else
                {
                    _logger.LogInformation($"付款完毕，通知采购中心：{CurrentContent.BusinessCode}**{CurrentContent.RelateId}");
                    await _daprClient.PublishEventAsync<PurchaseOrder>("pubsub-default", "fam-purchase-payfinished", new PurchaseOrder(CurrentContent.BusinessCode, 0, CurrentContent.RelateId.Value));
                }

                if (listHandlers.Last().ListPushKdPaymentUseInfos.Any())
                {
                    _logger.LogInformation($"推送游离付款单金额调整给金蝶：{listHandlers.Last().ListPushKdPaymentUseInfos.ToJson()}");
                    var _kingdeeApiClient = _serviceProvider.GetService<IKingdeeApiClient>();
                    var kind = await _kingdeeApiClient.SavePaymentAdjustment(listHandlers.Last().ListPushKdPaymentUseInfos);
                    if (kind != null && kind.Code != CodeStatusEnum.Success)
                    {
                        throw new ApplicationException(kind.Message);
                    }
                }
            }
            else
            {//预付没有选付款单
                if (Math.Round(advanceItem.ActualPayAmount, 2) > 0)
                {
                    var actualAmount = decimal.Parse(advanceItem.ActualPayAmount.ToString("F2"));
                    if (AdvancePayModeEnum.NotUseQuota == advanceItem.AdvancePayMode)
                    {
                        Payment payment = await AddPayment(advanceItem, CurrentPurchaseOrder, actualAmount, CurrentPurchaseOrder.CreatedBy, false, DateTime.MinValue);
                        //采购付款计划推送金蝶
                        var ret = await PushPurchasePayPlans(payment, advanceItem);
                        if (ret.Code == CodeStatusEnum.Failed)
                        {
                            throw new ApplicationException(ret.Message);
                        }
                    }
                    else
                    {
                        await _daprClient.PublishEventAsync<PurchaseOrder>("pubsub-default", "fam-purchase-payfinished", new PurchaseOrder(CurrentContent.BusinessCode, 0, CurrentContent.RelateId.Value));
                    }
                }
            }
        }
    }
}
