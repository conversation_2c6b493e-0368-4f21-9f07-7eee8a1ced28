﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface IPurchaseOrderAppService : IPurchaseAppService
    {
        Task<BaseResponseData<int>> PurchaseForceend(EventBusDTO input);

        Task<BaseResponseData<int>> RollBack(string purchaseCode);

        /// <summary>
        /// 生产进口增值税关税缴费单应付
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> GeneralCustomsDebt(Guid id, string code);

        Task<BaseResponseData<int>> ServicePurchaseCreated(Guid id, string code);
        Task<BaseResponseData<int>> GeneralCustomsPayment(Guid id, string code, string? requestId = null);
    }
}
